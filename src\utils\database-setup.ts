import { supabase } from './supabase';

// Database setup utility functions
export const setupDatabase = async () => {
  try {
    console.log('Checking database setup...');

    // Check if we can query the users table
    const { error } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    if (error) {
      console.error('Users table not accessible:', error);
      if (error.code === '42P01') {
        console.log('Tables do not exist. Please run the SQL scripts manually.');
      }
      return false;
    }

    console.log('Database setup complete!');
    return true;
  } catch (error) {
    console.error('Database setup error:', error);
    return false;
  }
};

// Helper function to generate class code
export const generateClassCode = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// Check if class code is unique
export const isClassCodeUnique = async (code: string): Promise<boolean> => {
  const { data, error } = await supabase
    .from('classes')
    .select('id')
    .eq('class_code', code)
    .single();
  
  return !data && !error;
};

// Generate unique class code
export const generateUniqueClassCode = async (): Promise<string> => {
  let code: string;
  let isUnique = false;
  
  do {
    code = generateClassCode();
    isUnique = await isClassCodeUnique(code);
  } while (!isUnique);
  
  return code;
};

// Create notification helper
export const createNotification = async (
  userId: string,
  title: string,
  message: string,
  type: 'grade' | 'assignment' | 'class' | 'ticket' | 'general',
  relatedId?: string
) => {
  const { data, error } = await supabase
    .from('notifications')
    .insert({
      user_id: userId,
      title,
      message,
      type,
      related_id: relatedId
    })
    .select()
    .single();
  
  if (error) {
    console.error('Error creating notification:', error);
    return null;
  }
  
  return data;
};

// Get user's unread notifications count
export const getUnreadNotificationsCount = async (userId: string): Promise<number> => {
  const { count, error } = await supabase
    .from('notifications')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', userId)
    .eq('is_read', false);
  
  if (error) {
    console.error('Error getting unread notifications count:', error);
    return 0;
  }
  
  return count || 0;
};

// Mark notification as read
export const markNotificationAsRead = async (notificationId: string) => {
  const { error } = await supabase
    .from('notifications')
    .update({ is_read: true })
    .eq('id', notificationId);
  
  if (error) {
    console.error('Error marking notification as read:', error);
    return false;
  }
  
  return true;
};

// Mark all notifications as read for a user
export const markAllNotificationsAsRead = async (userId: string) => {
  const { error } = await supabase
    .from('notifications')
    .update({ is_read: true })
    .eq('user_id', userId)
    .eq('is_read', false);
  
  if (error) {
    console.error('Error marking all notifications as read:', error);
    return false;
  }
  
  return true;
};
