import React, { useEffect, useState } from 'react';
import { CalendarIcon } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { getUserAssignments } from '../utils/supabase';
import CalendarView from '../components/calendar/CalendarView';
import BackButton from '../components/BackButton';
import LoadingSpinner from '../components/LoadingSpinner';
interface CalendarEvent {
  id: string;
  title: string;
  date: Date;
  classId: string;
  className: string;
  type: 'assignment' | 'exam' | 'other';
}
const Calendar = () => {
  const { user } = useAuth();
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchAssignments = async () => {
      if (!user) return;

      setIsLoading(true);
      try {
        const { data: assignments, error } = await getUserAssignments(user.id);

        if (error) {
          console.error('Error fetching assignments:', error);
          return;
        }

        // Convert assignments to calendar events
        const calendarEvents: CalendarEvent[] = (assignments || []).map((assignment: any) => ({
          id: assignment.id,
          title: assignment.title,
          date: new Date(assignment.due_date),
          classId: assignment.classes?.id || '',
          className: assignment.classes?.name || 'Unknown Class',
          type: 'assignment'
        }));

        setEvents(calendarEvents);
      } catch (error) {
        console.error('Error fetching assignments:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssignments();
  }, [user]);
  return <div className="max-w-7xl mx-auto">
      <div className="mb-6">
        <BackButton />
      </div>
      <h1 className="text-3xl font-bold mb-8">Calendar</h1>
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="large" />
        </div>
      ) : events.length > 0 ? (
        <CalendarView events={events} />
      ) : (
        <div className="bg-white border border-gray-200 rounded-lg p-8 text-center">
          <CalendarIcon className="h-12 w-12 mx-auto mb-3 text-gray-400" />
          <h3 className="text-lg font-medium mb-1">No upcoming deadlines</h3>
          <p className="text-gray-500">
            You don't have any assignments due soon
          </p>
        </div>
      )}
    </div>;
};
export default Calendar;