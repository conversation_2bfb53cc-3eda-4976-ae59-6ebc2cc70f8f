import React, { useState } from 'react';
import { supabase } from '../utils/supabase';

const TestDatabase = () => {
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const testDatabaseConnection = async () => {
    setLoading(true);
    setResults([]);
    
    const tests = [
      {
        name: 'Check users table',
        test: async () => {
          const { data, error } = await supabase.from('users').select('count').limit(1);
          return { success: !error, data, error };
        }
      },
      {
        name: 'Check classes table',
        test: async () => {
          const { data, error } = await supabase.from('classes').select('count').limit(1);
          return { success: !error, data, error };
        }
      },
      {
        name: 'Check assignments table',
        test: async () => {
          const { data, error } = await supabase.from('assignments').select('count').limit(1);
          return { success: !error, data, error };
        }
      },
      {
        name: 'Check auth user',
        test: async () => {
          const { data: { user }, error } = await supabase.auth.getUser();
          return { success: !error, data: user, error };
        }
      }
    ];

    const testResults = [];
    for (const test of tests) {
      try {
        const result = await test.test();
        testResults.push({
          name: test.name,
          ...result
        });
      } catch (error) {
        testResults.push({
          name: test.name,
          success: false,
          error
        });
      }
    }
    
    setResults(testResults);
    setLoading(false);
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Database Connection Test</h1>
      
      <button
        onClick={testDatabaseConnection}
        disabled={loading}
        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 mb-6"
      >
        {loading ? 'Testing...' : 'Test Database Connection'}
      </button>

      {results.length > 0 && (
        <div className="space-y-4">
          {results.map((result, index) => (
            <div
              key={index}
              className={`p-4 rounded-md border ${
                result.success 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-red-50 border-red-200'
              }`}
            >
              <h3 className={`font-medium ${
                result.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {result.name}: {result.success ? '✅ Success' : '❌ Failed'}
              </h3>
              
              {result.error && (
                <p className="text-red-600 text-sm mt-2">
                  Error: {result.error.message || JSON.stringify(result.error)}
                </p>
              )}
              
              {result.data && (
                <pre className="text-xs mt-2 bg-gray-100 p-2 rounded overflow-auto">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TestDatabase;
