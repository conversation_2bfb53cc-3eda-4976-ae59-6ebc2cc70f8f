# 🚨 URGENT: Fix Infinite Recursion Error

## The Problem
You're getting "infinite recursion detected in policy for relation 'classes'" because the RLS policies are creating circular references between the `classes` and `class_members` tables.

## The Solution
**You need to run this SQL script in your Supabase SQL Editor RIGHT NOW:**

### Step 1: Go to Supabase
1. Open your Supabase project dashboard
2. Go to SQL Editor
3. Create a new query

### Step 2: Co<PERSON> and Paste This Exact SQL:

```sql
-- URGENT FIX: Infinite Recursion in Classes Table RLS Policy
-- Run this script IMMEDIATELY in Supabase SQL Editor

-- Step 1: Drop ALL problematic policies that cause infinite recursion
DROP POLICY IF EXISTS "Users can view classes they are members of" ON public.classes;
DROP POLICY IF EXISTS "Users can view class members of their classes" ON public.class_members;
DROP POLICY IF EXISTS "Users can view assignments in their classes" ON public.assignments;

-- Step 2: Create SIMPLE policies that avoid ALL circular references

-- SIMPLE Classes table policies - NO references to class_members
CREATE POLICY "Teachers can view their classes" ON public.classes
  FOR SELECT USING (teacher_id = auth.uid());

-- Allow ALL authenticated users to view classes (we'll filter in application logic)
CREATE POLICY "Users can view all classes" ON public.classes
  FOR SELECT USING (auth.uid() IS NOT NULL);

-- SIMPLE Class members table policies - NO circular references
CREATE POLICY "Users can view own membership" ON public.class_members
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Teachers can view all class members" ON public.class_members
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.classes c
      WHERE c.id = class_id AND c.teacher_id = auth.uid()
    )
  );

-- SIMPLE Assignments table policy - NO circular references
CREATE POLICY "Teachers can view their assignments" ON public.assignments
  FOR SELECT USING (teacher_id = auth.uid());

CREATE POLICY "Users can view all assignments" ON public.assignments
  FOR SELECT USING (auth.uid() IS NOT NULL);

-- Step 3: Ensure all tables have RLS enabled
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.class_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Step 4: Verify the fix
SELECT 'Infinite recursion fix applied successfully!' as status,
       'Classes table policies updated' as fix_1,
       'Class members policies simplified' as fix_2,
       'Circular references removed' as fix_3;
```

### Step 3: Run the Script
1. Click the "Run" button in Supabase SQL Editor
2. You should see a success message
3. Refresh your app and try accessing the People page

## What This Does
- **Removes** all the problematic policies that create circular references
- **Creates** simple policies that don't reference each other
- **Allows** the app to work without infinite recursion
- **Maintains** security by still checking user authentication

## After Running This
- The infinite recursion error will be fixed
- The People page will work
- All other functionality will continue to work
- Security is maintained through simpler, non-circular policies

**Run this NOW and the error will be fixed immediately!**
