import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { PlusIcon, UsersIcon, BookOpenIcon } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { getUserClasses } from '../utils/supabase';
const Classes = () => {
  const {
    user,
    isTeacher
  } = useAuth();
  const [classes, setClasses] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchClasses = async () => {
      if (!user) return;

      setLoading(true);
      try {
        const { data, error } = await getUserClasses(user.id);
        if (error) {
          console.error('Error fetching classes:', error);
          return;
        }

        // Transform the data to match the expected format
        const transformedClasses = data?.map((membership: any) => ({
          id: membership.classes.id,
          name: membership.classes.name,
          subject: membership.classes.subject,
          description: membership.classes.description,
          color: membership.classes.color_scheme,
          teacher: membership.classes.users,
          role: membership.role,
          memberCount: 0, // We'll need to calculate this separately if needed
          joinedAt: membership.joined_at
        })) || [];

        setClasses(transformedClasses);
      } catch (error) {
        console.error('Error fetching classes:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchClasses();
  }, [user]);
  if (loading) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return <div className="max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Your Classes</h1>
        <div className="flex gap-3">
          {!isTeacher() && (
            <Link to="/classes/join" className="bg-green-600 text-white px-4 py-2 rounded-md flex items-center hover:bg-green-700">
              <PlusIcon className="h-5 w-5 mr-2" />
              Join Class
            </Link>
          )}
          {isTeacher() && (
            <Link to="/classes/create" className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center hover:bg-blue-700">
              <PlusIcon className="h-5 w-5 mr-2" />
              Create Class
            </Link>
          )}
        </div>
      </div>

      {classes.length === 0 ? (
        <div className="text-center py-12">
          <BookOpenIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No classes yet</h3>
          <p className="text-gray-500 mb-6">
            {isTeacher()
              ? "Create your first class to get started"
              : "Join a class using the class code provided by your teacher"
            }
          </p>
          {isTeacher() ? (
            <Link to="/classes/create" className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700">
              Create Your First Class
            </Link>
          ) : (
            <Link to="/classes/join" className="bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700">
              Join a Class
            </Link>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
          {classes.map((cls: any) => (
            <Link
              key={cls.id}
              to={`/classes/${cls.id}`}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow"
              style={{ borderTop: `4px solid ${cls.color}` }}
            >
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-xl font-bold text-gray-900">{cls.name}</h3>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  cls.role === 'teacher'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-green-100 text-green-800'
                }`}>
                  {cls.role}
                </span>
              </div>

              {cls.subject && (
                <p className="text-gray-600 mb-2">{cls.subject}</p>
              )}

              {cls.description && (
                <p className="text-gray-500 text-sm mb-4 line-clamp-2">{cls.description}</p>
              )}

              <div className="flex items-center justify-between text-sm text-gray-500">
                <div className="flex items-center">
                  <UsersIcon className="h-4 w-4 mr-1" />
                  <span>{cls.memberCount || 0} members</span>
                </div>
                {cls.role === 'student' && cls.teacher && (
                  <span>by {cls.teacher.name}</span>
                )}
              </div>
            </Link>
          ))}
        </div>
      )}
    </div>;
};
export default Classes;