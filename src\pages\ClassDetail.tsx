import React, { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { PlusIcon, FileTextIcon, CalendarIcon, UsersIcon, UserPlusIcon } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { getClassAssignments, getClassDetails } from '../utils/supabase';
import GradesTable from '../components/grades/GradesTable';
import BackButton from '../components/BackButton';
interface ClassInfo {
  name: string;
  color: string;
  students: number;
}
const ClassDetail = () => {
  const {
    classId
  } = useParams();
  const {
    isTeacher
  } = useAuth();
  const [activeTab, setActiveTab] = useState('assignments');
  const [assignments, setAssignments] = useState([]);
  const [grades, setGrades] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [classInfo, setClassInfo] = useState<ClassInfo | null>(null);
  useEffect(() => {
    const fetchClassData = async () => {
      setIsLoading(true);
      try {
        if (!classId) return;

        // Fetch class details
        const { data: classData, error: classError } = await getClassDetails(classId);
        if (classError) {
          throw classError;
        }

        if (classData) {
          setClassInfo({
            name: classData.name,
            color: classData.color_scheme || '#3B82F6',
            students: classData.class_members?.length || 0
          });
        }

        // Fetch assignments for this class
        const { data: assignmentData, error: assignmentError } = await getClassAssignments(classId);
        if (assignmentError) {
          console.error('Error fetching assignments:', assignmentError);
        } else {
          setAssignments(assignmentData || []);
        }

        // For now, we'll skip grades until we implement the grading system
        if (isTeacher()) {
          setGrades([]); // Will be implemented later
        }
      } catch (error) {
        console.error('Error fetching class data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    if (classId) {
      fetchClassData();
    }
  }, [classId, isTeacher]);
  if (isLoading) {
    return <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>;
  }
  if (!classInfo) {
    return <div className="max-w-7xl mx-auto text-center py-12">
        <h2 className="text-2xl font-semibold text-gray-900">
          Class not found
        </h2>
        <p className="mt-2 text-gray-600">
          The class you're looking for doesn't exist or you don't have access to
          it.
        </p>
        <Link to="/classes" className="mt-4 inline-block text-blue-600 hover:underline">
          Return to Classes
        </Link>
      </div>;
  }
  return <div className="max-w-7xl mx-auto">
      <div className="mb-4">
        <BackButton to="/classes" />
      </div>
      <div className={`${classInfo.color} text-white p-8 rounded-lg mb-6`}>
        <h1 className="text-3xl font-bold mb-2">{classInfo.name}</h1>
        <div className="flex items-center">
          <UsersIcon className="h-5 w-5 mr-2" />
          <span>{classInfo.students} students</span>
        </div>
      </div>
      <div className="bg-white border border-gray-200 rounded-lg mb-6">
        <div className="flex border-b border-gray-200">
          <button className={`px-6 py-4 font-medium ${activeTab === 'assignments' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-blue-600'}`} onClick={() => setActiveTab('assignments')}>
            Assignments
          </button>
          {isTeacher() && <button className={`px-6 py-4 font-medium ${activeTab === 'grades' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-blue-600'}`} onClick={() => setActiveTab('grades')}>
              Grades
            </button>}
          <button className={`px-6 py-4 font-medium ${activeTab === 'people' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-blue-600'}`} onClick={() => setActiveTab('people')}>
            People
          </button>
        </div>
        <div className="p-6">
          {activeTab === 'assignments' && <div>
              {isTeacher() && <div className="mb-6">
                  <Link to={`/classes/${classId}/create-assignment`} className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center hover:bg-blue-700 w-fit">
                    <PlusIcon className="h-5 w-5 mr-2" />
                    Create Assignment
                  </Link>
                </div>}
              {assignments.length > 0 ? <div className="space-y-4">
                  {assignments.map((assignment: any) => <Link key={assignment.id} to={`/classes/${classId}/assignments/${assignment.id}`} className="block bg-white border border-gray-200 rounded-lg p-4 hover:border-blue-500 transition">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start">
                          <FileTextIcon className="h-6 w-6 text-blue-600 mr-3 mt-1" />
                          <div>
                            <h3 className="font-medium">{assignment.title}</h3>
                            <p className="text-sm text-gray-500">
                              Max marks: {assignment.maxMarks}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center text-gray-500">
                            <CalendarIcon className="h-4 w-4 mr-1" />
                            <span className="text-sm">
                              Due{' '}
                              {new Date(assignment.dueDate).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    </Link>)}
                </div> : <div className="text-center py-8 text-gray-500">
                  <FileTextIcon className="h-12 w-12 mx-auto mb-3 text-gray-400" />
                  <h3 className="text-lg font-medium mb-1">
                    No assignments yet
                  </h3>
                  <p>
                    {isTeacher() ? 'Create your first assignment to get started' : "Your teacher hasn't created any assignments yet"}
                  </p>
                </div>}
            </div>}
          {activeTab === 'grades' && isTeacher() && <GradesTable grades={grades} onUpdateGrade={(id, marks) => {
          setGrades(grades.map((g: any) => g.id === id ? {
            ...g,
            marks
          } : g));
        }} />}
          {activeTab === 'people' && <div>
              {isTeacher() && <div className="mb-6">
                  <Link to={`/classes/${classId}/students`} className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center hover:bg-blue-700 w-fit">
                    <UserPlusIcon className="h-5 w-5 mr-2" />
                    Manage Students
                  </Link>
                </div>}
              <div className="divide-y divide-gray-200 border border-gray-200 rounded-lg">
                <div className="p-4 bg-gray-50">
                  <h3 className="font-medium">Teachers</h3>
                </div>
                <div className="p-4">
                  <div className="flex items-center">
                    <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                      <span className="text-blue-600 font-medium">T</span>
                    </div>
                    <div>
                      <p className="font-medium">Teacher</p>
                      <p className="text-sm text-gray-500">
                        <EMAIL>
                      </p>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-gray-50">
                  <h3 className="font-medium">Students</h3>
                </div>
                <div className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                        <span className="text-gray-600 font-medium">A</span>
                      </div>
                      <div>
                        <p className="font-medium">Alice Johnson</p>
                        <p className="text-sm text-gray-500">
                          <EMAIL>
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                        <span className="text-gray-600 font-medium">B</span>
                      </div>
                      <div>
                        <p className="font-medium">Bob Smith</p>
                        <p className="text-sm text-gray-500"><EMAIL></p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                        <span className="text-gray-600 font-medium">C</span>
                      </div>
                      <div>
                        <p className="font-medium">Charlie Brown</p>
                        <p className="text-sm text-gray-500">
                          <EMAIL>
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                        <span className="text-gray-600 font-medium">D</span>
                      </div>
                      <div>
                        <p className="font-medium">Diana Prince</p>
                        <p className="text-sm text-gray-500">
                          <EMAIL>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>}
        </div>
      </div>
    </div>;
};
export default ClassDetail;