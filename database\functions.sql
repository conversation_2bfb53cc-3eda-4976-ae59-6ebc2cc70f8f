-- Function to generate unique class codes
CREATE OR REPLACE FUNCTION generate_class_code()
RETURNS TEXT AS $$
DECLARE
  code TEXT;
  exists_check BOOLEAN;
BEGIN
  LOOP
    -- Generate a 6-character alphanumeric code
    code := upper(substring(md5(random()::text) from 1 for 6));
    
    -- Check if code already exists
    SELECT EXISTS(SELECT 1 FROM public.classes WHERE class_code = code) INTO exists_check;
    
    -- If code doesn't exist, return it
    IF NOT exists_check THEN
      RETURN code;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to automatically create user profile after signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', 'User'),
    COALESCE(NEW.raw_user_meta_data->>'role', 'student')
  );
  R<PERSON>URN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON>gger to automatically create user profile
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to create notification
CREATE OR REPLACE FUNCTION create_notification(
  p_user_id UUID,
  p_title TEXT,
  p_message TEXT,
  p_type TEXT,
  p_related_id UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  notification_id UUID;
BEGIN
  INSERT INTO public.notifications (user_id, title, message, type, related_id)
  VALUES (p_user_id, p_title, p_message, p_type, p_related_id)
  RETURNING id INTO notification_id;
  
  RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to notify class members about new assignments
CREATE OR REPLACE FUNCTION notify_assignment_created()
RETURNS TRIGGER AS $$
DECLARE
  member_record RECORD;
BEGIN
  -- Notify all students in the class about the new assignment
  FOR member_record IN 
    SELECT cm.user_id 
    FROM public.class_members cm 
    WHERE cm.class_id = NEW.class_id AND cm.role = 'student'
  LOOP
    PERFORM create_notification(
      member_record.user_id,
      'New Assignment',
      'A new assignment "' || NEW.title || '" has been posted.',
      'assignment',
      NEW.id
    );
  END LOOP;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for assignment notifications
CREATE TRIGGER assignment_created_notification
  AFTER INSERT ON public.assignments
  FOR EACH ROW EXECUTE FUNCTION notify_assignment_created();

-- Function to notify students about grades
CREATE OR REPLACE FUNCTION notify_grade_given()
RETURNS TRIGGER AS $$
BEGIN
  -- Only notify if grade was added or changed
  IF (OLD.grade IS NULL AND NEW.grade IS NOT NULL) OR 
     (OLD.grade IS NOT NULL AND NEW.grade IS NOT NULL AND OLD.grade != NEW.grade) THEN
    
    PERFORM create_notification(
      NEW.student_id,
      'Grade Updated',
      'Your submission has been graded. Score: ' || NEW.grade || '/' || 
      (SELECT max_marks FROM public.assignments WHERE id = NEW.assignment_id),
      'grade',
      NEW.id
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for grade notifications
CREATE TRIGGER grade_updated_notification
  AFTER UPDATE ON public.submissions
  FOR EACH ROW EXECUTE FUNCTION notify_grade_given();

-- Function to notify about ticket status changes
CREATE OR REPLACE FUNCTION notify_ticket_updated()
RETURNS TRIGGER AS $$
BEGIN
  -- Notify student about ticket status change
  IF OLD.status != NEW.status THEN
    PERFORM create_notification(
      NEW.student_id,
      'Ticket Status Updated',
      'Your ticket "' || NEW.title || '" status changed to: ' || NEW.status,
      'ticket',
      NEW.id
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for ticket notifications
CREATE TRIGGER ticket_updated_notification
  AFTER UPDATE ON public.tickets
  FOR EACH ROW EXECUTE FUNCTION notify_ticket_updated();
