import React, { useState } from 'react';
import { setupDatabase } from '../utils/database-setup';
import { CheckCircleIcon, XCircleIcon, LoaderIcon } from 'lucide-react';

const DatabaseSetup = () => {
  const [status, setStatus] = useState<'idle' | 'checking' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');

  const handleSetup = async () => {
    setStatus('checking');
    setMessage('Checking database setup...');
    
    try {
      const success = await setupDatabase();
      if (success) {
        setStatus('success');
        setMessage('Database is properly set up!');
      } else {
        setStatus('error');
        setMessage('Database setup incomplete. Please run the SQL scripts manually in Supabase dashboard.');
      }
    } catch (error) {
      setStatus('error');
      setMessage('Error checking database setup.');
      console.error('Database setup error:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white shadow rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Database Setup</h1>
          
          <div className="mb-6">
            <p className="text-gray-600 mb-4">
              Before using the application, we need to ensure the database tables are properly set up.
            </p>
            
            <button
              onClick={handleSetup}
              disabled={status === 'checking'}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
            >
              {status === 'checking' && <LoaderIcon className="animate-spin h-4 w-4 mr-2" />}
              Check Database Setup
            </button>
          </div>

          {status !== 'idle' && (
            <div className={`p-4 rounded-md flex items-center ${
              status === 'success' ? 'bg-green-50 text-green-800' :
              status === 'error' ? 'bg-red-50 text-red-800' :
              'bg-blue-50 text-blue-800'
            }`}>
              {status === 'success' && <CheckCircleIcon className="h-5 w-5 mr-2" />}
              {status === 'error' && <XCircleIcon className="h-5 w-5 mr-2" />}
              {status === 'checking' && <LoaderIcon className="animate-spin h-5 w-5 mr-2" />}
              {message}
            </div>
          )}

          {status === 'error' && (
            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
              <h3 className="text-lg font-medium text-yellow-800 mb-2">Manual Setup Required</h3>
              <p className="text-yellow-700 mb-4">
                Please follow these steps to set up the database manually:
              </p>
              <ol className="list-decimal list-inside text-yellow-700 space-y-2">
                <li>Go to your <a href="https://supabase.com/dashboard" target="_blank" rel="noopener noreferrer" className="underline">Supabase Dashboard</a></li>
                <li>Select your project</li>
                <li>Go to the "SQL Editor" tab</li>
                <li>Click "New Query"</li>
                <li>Copy and paste the SQL scripts from the database folder</li>
                <li>Run each script in order: schema.sql, rls_policies.sql, functions.sql</li>
                <li>Return here and click "Check Database Setup" again</li>
              </ol>
            </div>
          )}

          {status === 'success' && (
            <div className="mt-6">
              <a
                href="/login"
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
              >
                Continue to Login
              </a>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DatabaseSetup;
