0- Credentials to be used throughout the project: 
Supabase URL: https://nrvautzqoohgwqjyjheg.supabase.co
Supabase ANON key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ydmF1dHpxb29oZ3dxanlqaGVnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMjE5OTMsImV4cCI6MjA2NjU5Nzk5M30.H-W9AjdGhWFoxVSEHqqI4elf5SnP4q80qSqlXQwHC0Y
Cloudinary Cloud name: 724224496477429
Cloudinary API Key: 133393639943987
Cloudinary API Secret: NrufH498l_I55ABLVm0MW5IcnxE

1. Plan a thorough database for the website on supabase including features for:
- Users creating 1 profile. The user will have option to join or create class so there is no need for separate teacher and student ids/profiles.
- Class/classes joined by each user
- Class/classes created by each user with all the details like name, description, code, color etc
- Assignments created by each user for each class, with assignment title, description, marks, date, and content. (AI Generated Assignments as well.)
- Assignments submitted by each user for each class with cloudinary link for the uploaded file. (save the OCRd text as well)
- Grades received by each student for each assignment in each class
- Simple Email Password Sign in and sign up
- Students grades tickets with status and description
- Notifications modal 

! Mostly everything must be updatable so carefully design the RLS. 

2. Make the website responsive for phone (make the sidebar collapsable as well) and desktop.
3. To-dos for class teachers:
a. Can generate AI assignment, upload a file as assignment, or the write the assignment in website with all the necessary fields.
b. Should be able to see the submissions for each student in each class. Both Submitted assignment, OCRd text and grade (editable) must be viewable.
c. Should be able to see Teaching Classes section.

4. To-dos for class students:
a. When submitting their assignment, only upload the file and click on submit assignment. THe OCR and Grading API calls should happen automatically. 
b. Must be able to see their submission with their OCRd text and Grade for each assignment in each class.
c. In calendar, must be able to see the upcoming deadlines.
d. Should be able to see Enrolled Classes section.
e. Must be able to raise a concern for their grades in each class for each assignment, 

5. People Must be visible in each class. Teacher will see student and manage them, students will see teacher and other students.

6. Settings section for users, where they can unenroll from classes, update their profile etc.

7. A notification modal, where new changes, such as grades given, ticket closed, class updates (such as new assignment, or removed from a class etc, new student joined) must show for each user.

8. No mock data use. Only use data from Supabase and cloudinary. 

9. Make sure all the routes are working

10. VERY IMPORTANT: MAKE SURE THE API CALLS TO THE BACKEND ARE SENDING CORRECT PAYLOAD AND RECEIVING CORRECT RESPONSE. (Right now it is working so dont do anything). 
    use the data from responses to show in the UI and save in the database. 

11. Add a logo for the app. i have an svg and a png, whichever is needed, let me know where to place it with what name.

12. Changes must be updated in realtime so users know whats happening. 

13. Make sure all the features, pages, components, routes and everything else is properly working and complete.