-- Fix Teacher Membership Issue
-- This script ensures teachers are properly added to their own classes

-- Step 1: Check for classes where the teacher is not a member
-- and add them as members

INSERT INTO public.class_members (class_id, user_id, role)
SELECT 
    c.id as class_id,
    c.teacher_id as user_id,
    'teacher' as role
FROM public.classes c
LEFT JOIN public.class_members cm ON (c.id = cm.class_id AND c.teacher_id = cm.user_id)
WHERE cm.id IS NULL
ON CONFLICT (class_id, user_id) DO NOTHING;

-- Step 2: Verify the fix
SELECT 
    c.name as class_name,
    c.class_code,
    u.name as teacher_name,
    u.email as teacher_email,
    CASE 
        WHEN cm.id IS NOT NULL THEN 'YES'
        ELSE 'NO'
    END as is_member
FROM public.classes c
JOIN public.users u ON c.teacher_id = u.id
LEFT JOIN public.class_members cm ON (c.id = cm.class_id AND c.teacher_id = cm.user_id)
ORDER BY c.created_at DESC;

SELECT 'Teacher membership fix completed!' as status;
