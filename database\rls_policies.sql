-- Enable Row Level Security on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.class_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON public.users
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Classes table policies
CREATE POLICY "Users can view classes they are members of" ON public.classes
  FOR SELECT USING (
    id IN (
      SELECT class_id FROM public.class_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Teachers can create classes" ON public.classes
  FOR INSERT WITH CHECK (
    auth.uid() = teacher_id AND
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'teacher'
    )
  );

CREATE POLICY "Teachers can update their own classes" ON public.classes
  FOR UPDATE USING (
    auth.uid() = teacher_id AND
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'teacher'
    )
  );

CREATE POLICY "Teachers can delete their own classes" ON public.classes
  FOR DELETE USING (
    auth.uid() = teacher_id AND
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'teacher'
    )
  );

-- Class members table policies
CREATE POLICY "Users can view class members of their classes" ON public.class_members
  FOR SELECT USING (
    -- Allow users to see their own membership record
    user_id = auth.uid() OR
    -- Allow teachers to see all members of their classes
    EXISTS (
      SELECT 1 FROM public.classes c
      WHERE c.id = class_id AND c.teacher_id = auth.uid()
    )
  );

CREATE POLICY "Teachers can add students to their classes" ON public.class_members
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.classes 
      WHERE id = class_id AND teacher_id = auth.uid()
    )
  );

CREATE POLICY "Students can join classes" ON public.class_members
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND role = 'student'
  );

CREATE POLICY "Teachers can add themselves to their classes" ON public.class_members
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND role = 'teacher' AND
    EXISTS (
      SELECT 1 FROM public.classes
      WHERE id = class_id AND teacher_id = auth.uid()
    )
  );

CREATE POLICY "Users can leave classes" ON public.class_members
  FOR DELETE USING (user_id = auth.uid());

CREATE POLICY "Teachers can remove students from their classes" ON public.class_members
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.classes 
      WHERE id = class_id AND teacher_id = auth.uid()
    )
  );

-- Assignments table policies
CREATE POLICY "Users can view assignments in their classes" ON public.assignments
  FOR SELECT USING (
    -- Teachers can see assignments in their classes
    EXISTS (
      SELECT 1 FROM public.classes c
      WHERE c.id = class_id AND c.teacher_id = auth.uid()
    ) OR
    -- Students can see assignments in classes they're members of
    EXISTS (
      SELECT 1 FROM public.class_members cm
      WHERE cm.class_id = class_id AND cm.user_id = auth.uid() AND cm.role = 'student'
    )
  );

CREATE POLICY "Teachers can create assignments in their classes" ON public.assignments
  FOR INSERT WITH CHECK (
    auth.uid() = teacher_id AND
    EXISTS (
      SELECT 1 FROM public.classes 
      WHERE id = class_id AND teacher_id = auth.uid()
    )
  );

CREATE POLICY "Teachers can update their assignments" ON public.assignments
  FOR UPDATE USING (auth.uid() = teacher_id);

CREATE POLICY "Teachers can delete their assignments" ON public.assignments
  FOR DELETE USING (auth.uid() = teacher_id);

-- Submissions table policies
CREATE POLICY "Students can view their own submissions" ON public.submissions
  FOR SELECT USING (student_id = auth.uid());

CREATE POLICY "Teachers can view submissions in their classes" ON public.submissions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.assignments a
      JOIN public.classes c ON a.class_id = c.id
      WHERE a.id = assignment_id AND c.teacher_id = auth.uid()
    )
  );

CREATE POLICY "Students can create their own submissions" ON public.submissions
  FOR INSERT WITH CHECK (
    student_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM public.assignments a
      JOIN public.class_members cm ON a.class_id = cm.class_id
      WHERE a.id = assignment_id AND cm.user_id = auth.uid() AND cm.role = 'student'
    )
  );

CREATE POLICY "Students can update their own submissions" ON public.submissions
  FOR UPDATE USING (student_id = auth.uid());

CREATE POLICY "Teachers can update submissions in their classes" ON public.submissions
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.assignments a
      JOIN public.classes c ON a.class_id = c.id
      WHERE a.id = assignment_id AND c.teacher_id = auth.uid()
    )
  );

-- Tickets table policies
CREATE POLICY "Students can view their own tickets" ON public.tickets
  FOR SELECT USING (student_id = auth.uid());

CREATE POLICY "Teachers can view tickets for their classes" ON public.tickets
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.submissions s
      JOIN public.assignments a ON s.assignment_id = a.id
      JOIN public.classes c ON a.class_id = c.id
      WHERE s.id = submission_id AND c.teacher_id = auth.uid()
    )
  );

CREATE POLICY "Students can create tickets for their submissions" ON public.tickets
  FOR INSERT WITH CHECK (
    student_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM public.submissions 
      WHERE id = submission_id AND student_id = auth.uid()
    )
  );

CREATE POLICY "Students can update their own tickets" ON public.tickets
  FOR UPDATE USING (student_id = auth.uid());

CREATE POLICY "Teachers can update tickets for their classes" ON public.tickets
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.submissions s
      JOIN public.assignments a ON s.assignment_id = a.id
      JOIN public.classes c ON a.class_id = c.id
      WHERE s.id = submission_id AND c.teacher_id = auth.uid()
    )
  );

-- Notifications table policies
CREATE POLICY "Users can view their own notifications" ON public.notifications
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "System can create notifications" ON public.notifications
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update their own notifications" ON public.notifications
  FOR UPDATE USING (user_id = auth.uid());
