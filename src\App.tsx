import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/layout/Layout';
import Dashboard from './pages/Dashboard';
import Classes from './pages/Classes';
import ClassDetail from './pages/ClassDetail';
import Calendar from './pages/Calendar';
import People from './pages/People';
import AssignmentDetail from './pages/AssignmentDetail';
import CreateAssignment from './pages/CreateAssignment';
import SubmitAssignment from './pages/SubmitAssignment';
import ReviewSubmission from './pages/ReviewSubmission';
import Login from './pages/Login';
import DatabaseSetup from './pages/DatabaseSetup';
import TestDatabase from './pages/TestDatabase';
import Profile from './pages/Profile';
import CreateClass from './pages/CreateClass';
import JoinClass from './pages/JoinClass';
import ClassStudents from './pages/ClassStudents';
import Tickets from './pages/Tickets';
import { AuthProvider } from './context/AuthContext';
import { NotificationProvider } from './context/NotificationContext';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
export function App() {
  return <AuthProvider>
      <NotificationProvider>
        <Router>
          <ToastContainer position="top-right" autoClose={3000} />
          <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/setup" element={<DatabaseSetup />} />
          <Route path="/test" element={<TestDatabase />} />
          <Route path="/" element={<Layout />}>
            <Route index element={<Dashboard />} />
            <Route path="classes" element={<Classes />} />
            <Route path="classes/create" element={<CreateClass />} />
            <Route path="classes/join" element={<JoinClass />} />
            <Route path="classes/:classId" element={<ClassDetail />} />
            <Route path="classes/:classId/students" element={<ClassStudents />} />
            <Route path="classes/:classId/assignments/:assignmentId" element={<AssignmentDetail />} />
            <Route path="classes/:classId/create-assignment" element={<CreateAssignment />} />
            <Route path="classes/:classId/assignments/:assignmentId/submit" element={<SubmitAssignment />} />
            <Route path="assignments/:assignmentId/submissions/:submissionId" element={<ReviewSubmission />} />
            <Route path="calendar" element={<Calendar />} />
            <Route path="people" element={<People />} />
            <Route path="tickets" element={<Tickets />} />
            <Route path="profile" element={<Profile />} />
          </Route>
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Router>
    </NotificationProvider>
  </AuthProvider>;
}