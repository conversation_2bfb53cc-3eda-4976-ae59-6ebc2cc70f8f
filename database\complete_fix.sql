-- COMPLETE FIX: Database RLS Policies and Authentication Issues
-- Run this script in Supabase SQL Editor to fix all issues

-- Step 1: Drop ALL existing policies to start fresh
DO $$ 
DECLARE
    r RECORD;
BEGIN
    -- Drop all policies on all tables
    FOR r IN (SELECT schemaname, tablename, policyname FROM pg_policies WHERE schemaname = 'public') 
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || r.policyname || '" ON ' || r.schemaname || '.' || r.tablename;
    END LOOP;
END $$;

-- Step 2: Ensure all tables exist and have RLS enabled
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.class_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Step 3: Create SIMPLE, NON-CIRCULAR policies

-- Users table policies (basic, no circular references)
CREATE POLICY "users_select_own" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "users_insert_own" ON public.users
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "users_update_own" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- Classes table policies (simple, no references to class_members)
CREATE POLICY "classes_select_all" ON public.classes
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "classes_insert_teachers" ON public.classes
  FOR INSERT WITH CHECK (auth.uid() = teacher_id);

CREATE POLICY "classes_update_teachers" ON public.classes
  FOR UPDATE USING (auth.uid() = teacher_id);

CREATE POLICY "classes_delete_teachers" ON public.classes
  FOR DELETE USING (auth.uid() = teacher_id);

-- Class members table policies (simple, no circular references)
CREATE POLICY "class_members_select_own" ON public.class_members
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "class_members_select_teachers" ON public.class_members
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.classes c 
      WHERE c.id = class_id AND c.teacher_id = auth.uid()
    )
  );

CREATE POLICY "class_members_insert_own" ON public.class_members
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "class_members_insert_teachers" ON public.class_members
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.classes c 
      WHERE c.id = class_id AND c.teacher_id = auth.uid()
    )
  );

CREATE POLICY "class_members_delete_own" ON public.class_members
  FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "class_members_delete_teachers" ON public.class_members
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.classes c 
      WHERE c.id = class_id AND c.teacher_id = auth.uid()
    )
  );

-- Assignments table policies (simple)
CREATE POLICY "assignments_select_all" ON public.assignments
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "assignments_insert_teachers" ON public.assignments
  FOR INSERT WITH CHECK (auth.uid() = teacher_id);

CREATE POLICY "assignments_update_teachers" ON public.assignments
  FOR UPDATE USING (auth.uid() = teacher_id);

CREATE POLICY "assignments_delete_teachers" ON public.assignments
  FOR DELETE USING (auth.uid() = teacher_id);

-- Submissions table policies (simple)
CREATE POLICY "submissions_select_own" ON public.submissions
  FOR SELECT USING (auth.uid() = student_id);

CREATE POLICY "submissions_select_teachers" ON public.submissions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.assignments a
      WHERE a.id = assignment_id AND a.teacher_id = auth.uid()
    )
  );

CREATE POLICY "submissions_insert_own" ON public.submissions
  FOR INSERT WITH CHECK (auth.uid() = student_id);

CREATE POLICY "submissions_update_own" ON public.submissions
  FOR UPDATE USING (auth.uid() = student_id);

CREATE POLICY "submissions_update_teachers" ON public.submissions
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.assignments a
      WHERE a.id = assignment_id AND a.teacher_id = auth.uid()
    )
  );

-- Tickets table policies (simple)
CREATE POLICY "tickets_select_own" ON public.tickets
  FOR SELECT USING (auth.uid() = student_id);

CREATE POLICY "tickets_insert_own" ON public.tickets
  FOR INSERT WITH CHECK (auth.uid() = student_id);

CREATE POLICY "tickets_update_own" ON public.tickets
  FOR UPDATE USING (auth.uid() = student_id);

-- Notifications table policies (simple)
CREATE POLICY "notifications_select_own" ON public.notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "notifications_insert_all" ON public.notifications
  FOR INSERT WITH CHECK (true);

CREATE POLICY "notifications_update_own" ON public.notifications
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "notifications_delete_own" ON public.notifications
  FOR DELETE USING (auth.uid() = user_id);

-- Step 4: Success message
SELECT 'Database policies fixed successfully!' as status,
       'All circular references removed' as fix_1,
       'Simple policies implemented' as fix_2,
       'Authentication should work now' as fix_3;
